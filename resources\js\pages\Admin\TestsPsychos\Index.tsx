import { Head, router } from '@inertiajs/react';
import { PageProps, TestPsycho, Lieu, BreadcrumbItem, PaginatedData } from '@/types';
import TabNavigation from '@/components/TabNavigation';
import DataTable from '@/components/DataTable';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

interface TestsPsychosPageProps extends PageProps {
    tests: PaginatedData<TestPsycho>;
    lieux: Lieu[];
    isArchive: boolean;
}

export default function Index({ tests, lieux, isArchive = false }: TestsPsychosPageProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isImportOpen, setIsImportOpen] = useState(false);
    const [editingTest, setEditingTest] = useState<TestPsycho | null>(null);

    const form = useForm({
        date: '',
        lieu_id: '',
        places_disponibles: '',
        prix: '',
        reference: '',
    });

    const importForm = useForm({
        file: null as File | null,
    });

    const columns = [
        {
            key: 'reference',
            label: 'Référence',
        },
        {
            key: 'date',
            label: 'Date',
            render: (value: unknown): React.ReactNode => {
                if (typeof value === 'string') {
                    return format(parseISO(value), 'dd/MM/yyyy', { locale: fr });
                }
                return '';
            }
        },

        {
            key: 'lieu',
            label: 'Lieu',
            render: (_value: unknown, row: Record<string, unknown>): React.ReactNode => {
                const test = row as unknown as TestPsycho;
                return test.lieu ?
                (<div className="flex items-center gap-2 min-w-45">
                    <div className="grid flex-1 text-left text-sm leading-tight">
                        <span title={`${test.lieu.nom} (${test.lieu.ville?.nom})`} className="truncate font-medium">
                            {`${test.lieu.nom} (${test.lieu.ville?.nom})`}
                        </span>
                    </div>
                </div>)

                 : '';
            }
        },
        // {
        //     key: 'places_disponibles',
        //     label: 'Places disponibles',
        //     render: (value: unknown): React.ReactNode => {
        //         return <div className="text-center">{value as React.ReactNode}</div>;
        //     },
        // },
        {
            key: 'prix',
            label: 'Prix',
            render: (value: unknown): React.ReactNode => {
                if (typeof value === 'number' || typeof value === 'string') {
                    return `${value} €`;
                }
                return '';
            }
        },
        {
            key: 'reservations_count',
            label: 'Réservations',
            render: (value: unknown): React.ReactNode => {
                return <div>{value as React.ReactNode}</div>;
            },
        },
    ];

    const handleAdd = () => {
        setEditingTest(null);
        form.reset();
        setIsOpen(true);
    };

    const handleImport = () => {
        importForm.reset();
        setIsImportOpen(true);
    };

    const handleImportSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        importForm.post(route('admin.tests-psychos.import'), {
            forceFormData: true,
            onSuccess: () => {
                setIsImportOpen(false);
                // toast.success('Tests psychotechniques importés avec succès');
            },
            onError: (errors) => {
                console.log(errors);
                toast.error('Erreur lors de l\'importation : ' + (errors.file || errors.message || 'Erreur inconnue'));
            }
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            importForm.setData('file', e.target.files[0]);
        }
    };

    const handleEdit = (row: Record<string, unknown>) => {
        const test = row as unknown as TestPsycho;
        setEditingTest(test);

        // Vérifier que les propriétés existent avant d'appeler toString()
        const lieu_id = test.lieu?.id !== undefined ? test.lieu.id.toString() : '';
        const places_disponibles = test.places_disponibles !== undefined ? test.places_disponibles.toString() : '';
        const prix = test.prix !== undefined ? test.prix.toString() : '';

        form.setData({
            date: test.date || '',
            lieu_id: lieu_id,
            places_disponibles: places_disponibles,
            prix: prix,
            reference: test.reference || '',
        });
        setIsOpen(true);
    };

    const handleDelete = (test: TestPsycho) => {
        if (confirm(`Êtes-vous sûr de vouloir supprimer le test psychotechnique "${test.reference}" ?`)) {
            router.delete(`/admin/tests-psychos/${test.id}`);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingTest) {
            form.put(`/admin/tests-psychos/${editingTest.id}`, {
                onSuccess: () => setIsOpen(false),
            });
        } else {
            form.post('/admin/tests-psychos', {
                onSuccess: () => setIsOpen(false),
            });
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Tests psychotechniques',
            href: '/admin/tests-psychos',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Tests psychotechniques" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <TabNavigation
                    tabs={[
                        {
                            name: 'Tests à venir',
                            href: route('admin.tests-psychos.index'),
                            current: !isArchive,
                        },
                        {
                            name: 'Archives',
                            href: route('admin.tests-psychos.archive'),
                            current: isArchive,
                        },
                    ]}
                />
                <DataTable
                    title="Tests psychotechniques"
                    columns={columns}
                    data={tests.data.map(test => ({
                        id: test.id,
                        reference: test.reference,
                        date: test.date,

                        lieu: test.lieu,
                        places_disponibles: test.places_disponibles,
                        prix: test.prix,
                        reservations_count: test.reservations_count,
                    }))}
                    onAdd={handleAdd}
                    onImport={handleImport}
                    onEdit={handleEdit}
                    onDelete={(row) => handleDelete(row as unknown as TestPsycho)}
                    pagination={{
                        links: tests.links,
                        from: tests.from,
                        to: tests.to,
                        total: tests.total
                    }}
                />
            </div>

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[900px]">
                    <DialogHeader>
                        <DialogTitle>
                            {editingTest ? 'Modifier le test psychotechnique' : 'Ajouter un test psychotechnique'}
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label htmlFor="reference" className="text-sm font-medium">Référence</label>
                            <Input
                                id="reference"
                                value={form.data.reference}
                                onChange={e => form.setData('reference', e.target.value)}
                                required
                            />
                            {form.errors.reference && <p className="text-sm text-red-500">{form.errors.reference}</p>}
                        </div>
                        <div className="space-y-2">
                            <label htmlFor="date" className="text-sm font-medium">Date</label>
                            <Input
                                id="date"
                                type="date"
                                value={form.data.date}
                                onChange={e => form.setData('date', e.target.value)}
                                required
                            />
                            {form.errors.date && <p className="text-sm text-red-500">{form.errors.date}</p>}
                        </div>

                        <div className="space-y-2">
                            <label htmlFor="lieu_id" className="text-sm font-medium">Lieu</label>
                            <Select
                                value={form.data.lieu_id}
                                onValueChange={value => form.setData('lieu_id', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Sélectionner un lieu" />
                                </SelectTrigger>
                                <SelectContent>
                                    {lieux.map(lieu => (
                                        <SelectItem key={lieu.id} value={lieu.id.toString()}>
                                            {lieu.nom} ({lieu.ville?.nom})
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {form.errors.lieu_id && <p className="text-sm text-red-500">{form.errors.lieu_id}</p>}
                        </div>
                        <div className="space-y-2">
                            <label htmlFor="places_disponibles" className="text-sm font-medium">Places disponibles</label>
                            <Input
                                id="places_disponibles"
                                type="number"
                                min="1"
                                value={form.data.places_disponibles}
                                onChange={e => form.setData('places_disponibles', e.target.value)}
                                required
                            />
                            {form.errors.places_disponibles && <p className="text-sm text-red-500">{form.errors.places_disponibles}</p>}
                        </div>
                        <div className="space-y-2">
                            <label htmlFor="prix" className="text-sm font-medium">Prix (€)</label>
                            <Input
                                id="prix"
                                type="number"
                                min="0"
                                step="0.01"
                                value={form.data.prix}
                                onChange={e => form.setData('prix', e.target.value)}
                                required
                            />
                            {form.errors.prix && <p className="text-sm text-red-500">{form.errors.prix}</p>}
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingTest ? 'Mettre à jour' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            Importer des tests psychotechniques
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleImportSubmit} className="space-y-4">
                        <div>
                            <Label htmlFor="file">Fichier Excel</Label>
                            <Input
                                id="file"
                                type="file"
                                accept=".xlsx,.xls"
                                onChange={handleFileChange}
                                className="mt-1"
                            />
                            {importForm.errors.file && (
                                <p className="text-sm text-red-500 mt-1">{importForm.errors.file}</p>
                            )}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                            <p>Le fichier doit contenir les colonnes suivantes :</p>
                            <ul className="list-disc pl-5 mt-2">
                                <li>date (format: DD/MM/YYYY)</li>
                                <li>prix</li>
                                <li>places_disponibles</li>
                                <li>lieu_id</li>
                            </ul>
                            <p className="mt-2">
                                Veuillez utiliser un fichier Excel (.xlsx ou .xls) avec les colonnes indiquées ci-dessus.
                            </p>
                            <p className="mt-2">
                                <strong>Note:</strong> Les dates peuvent être au format texte (JJ/MM/AAAA) ou au format numérique Excel.
                            </p>
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsImportOpen(false)}
                            >
                                Annuler
                            </Button>
                            <Button type="submit" disabled={importForm.processing}>
                                Importer
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}





