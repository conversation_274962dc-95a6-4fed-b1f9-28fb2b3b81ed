import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import FrontLayout from '@/layouts/front-layout';
import { Link, router } from '@inertiajs/react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Calendar, Euro, LayoutGrid, MapPin, Table as TableIcon } from 'lucide-react';
import { useState } from 'react';

interface PaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

export default function TestsPsychotechniques({
  tests,
  departements,
  filters,
}: {
  tests: {
    data: Array<{
      id: number;
      reference: string;
      date: string;

      places_disponibles: number;
      prix: number;
      lieu: {
        nom: string;
        adresse: string;
        ville: {
          nom: string;
          departement: {
            nom: string;
          };
        };
      };
    }>;
    links: PaginationLink[];
    from: number;
    to: number;
    total: number;
  };
  departements: Array<{
    id: number;
    nom: string;
    code: string;
  }>;
  typeTests: Array<{
    id: number;
    nom: string;
    description: string | null;
  }>;
  filters: {
    departement?: string;
    mois?: string;
  };
}) {
  const [departement, setDepartement] = useState(filters.departement || 'all');
  const [mois, setMois] = useState(filters.mois || 'all');
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');

  const handleSearch = () => {
    router.get(
      route('tests-psychotechniques'),
      { departement, mois },
      {
        preserveState: true,
        preserveScroll: true,
      },
    );
  };

  const Pagination = ({ links }: { links: PaginationLink[] }) => {
    // Récupérer les filtres actuels
    const [departement, mois] = [filters.departement, filters.mois];

    return (
      <div className="flex flex-col items-center gap-4 overflow-x-scroll max-w-[100%]">
        <div className="flex items-center gap-2 max-w-[100%]">
          {links.map((link, index) => {
            // Traduire les labels de pagination
            const label = link.label
              .replace('Previous', '')
              .replace('Next', '')
              .replace(/^&laquo;\s*/, '«')
              .replace(/&raquo;$/, '»');

            if (!link.url) {
              return (
                <Button key={index} variant="outline" size="icon" disabled className="h-8 w-8">
                  <span>{label}</span>
                </Button>
              );
            }

            // Construire l'URL avec les filtres
            const url = new URL(link.url);
            if (departement && departement !== 'all') {
              url.searchParams.set('departement', departement);
            }
            if (mois && mois !== 'all') {
              url.searchParams.set('mois', mois);
            }

            return (
              <Button key={index} variant={link.active ? 'default' : 'outline'} size="icon" className="h-8 w-8" asChild>
                <Link href={url.toString()} preserveScroll preserveState>
                  <span>{label}</span>
                </Link>
              </Button>
            );
          })}
        </div>
        <div className="text-muted-foreground text-sm">
          Affichage des tests {tests.from} à {tests.to} sur {tests.total}
        </div>
      </div>
    );
  };

  const renderTableView = () => (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="hidden w-[150px] text-center font-bold md:table-cell">DATE</TableHead>

            <TableHead className="hidden w-[400px] text-center font-bold md:table-cell">LIEUX</TableHead>
            <TableHead className="hidden w-[400px] text-center font-bold md:table-cell">ADRESSE/VILLES</TableHead>
            {/* <TableHead className="w-[120px] font-bold text-center">PLACES LIBRES</TableHead> */}
            <TableHead className="hidden w-[100px] text-center font-bold md:table-cell">PRIX</TableHead>
            <TableHead className="table-cell w-[100px] text-center font-bold md:hidden">DETAILS</TableHead>
            <TableHead className="w-[200px] text-center font-bold">RESERVATION</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tests.data.map((test) => (
            <TableRow key={test.id}>
              <TableCell className="hidden text-center md:table-cell">
                <div className="font-medium">{format(parseISO(test.date), 'dd MMMM yyyy', { locale: fr })}</div>
              </TableCell>

              <TableCell className="hidden text-center md:table-cell">
                <div className="font-bold">{test.lieu.nom}</div>
              </TableCell>
              <TableCell className="hidden text-center md:table-cell">
                <div className="font-bold">{test.lieu.adresse}</div>
                <div className="font-medium">
                  <span className="text-muted-foreground text-sm">{test.lieu.ville.nom} </span>
                  {test.lieu.ville.departement.nom}
                </div>
              </TableCell>
              {/* <TableCell className="text-center">
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-sm font-medium text-green-800 dark:bg-green-900 dark:text-green-100">
                  {test.places_disponibles}
                </span>
              </TableCell> */}
              <TableCell className="hidden text-center font-medium md:table-cell">{test.prix} €</TableCell>
              <TableCell className="table-cell text-center font-medium md:hidden">
                <div className="font-medium">{format(parseISO(test.date), 'dd MMMM yyyy', { locale: fr })}</div>
                <div className="font-bold">{test.lieu.nom}</div>
                <span className="text-muted-foreground text-sm">{test.lieu.ville.nom} </span>
                {test.lieu.ville.departement.nom} <br />
                {test.prix} €
              </TableCell>
              <TableCell className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <Button asChild>
                    <Link href={route('reservation-test.show', test.id)}>Réserver</Link>
                  </Button>
                  {/* <Button variant="outline">Référence</Button> */}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <FrontLayout title="Tests psychotechniques">
      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-6 text-3xl font-bold">Tests psychotechniques</h1>

        <Card className="mb-4">
          <CardContent>
            <div className="mt-4 grid gap-4 md:grid-cols-4">
              <div>
                <label htmlFor="departement" className="mb-1 block text-sm font-medium">
                  Département
                </label>
                <Select value={departement} onValueChange={setDepartement}>
                  <SelectTrigger id="departement">
                    <SelectValue placeholder="Tous les départements" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les départements</SelectItem>
                    {departements.map((dep) => (
                      <SelectItem key={dep.id} value={dep.code}>
                        {dep.code} - {dep.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label htmlFor="mois" className="mb-1 block text-sm font-medium">
                  Mois
                </label>
                <Select value={mois} onValueChange={setMois}>
                  <SelectTrigger id="mois">
                    <SelectValue placeholder="Tous les mois" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les mois</SelectItem>
                    <SelectItem value="1">Janvier</SelectItem>
                    <SelectItem value="2">Février</SelectItem>
                    <SelectItem value="3">Mars</SelectItem>
                    <SelectItem value="4">Avril</SelectItem>
                    <SelectItem value="5">Mai</SelectItem>
                    <SelectItem value="6">Juin</SelectItem>
                    <SelectItem value="7">Juillet</SelectItem>
                    <SelectItem value="8">Août</SelectItem>
                    <SelectItem value="9">Septembre</SelectItem>
                    <SelectItem value="10">Octobre</SelectItem>
                    <SelectItem value="11">Novembre</SelectItem>
                    <SelectItem value="12">Décembre</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button className="w-full" onClick={handleSearch}>
                  Rechercher
                </Button>
              </div>

              <div className="flex items-end justify-end">
                <div className="flex items-center gap-2 rounded-md border p-1">
                  <Button variant={viewMode === 'table' ? 'default' : 'ghost'} size="sm" onClick={() => setViewMode('table')}>
                    <TableIcon className="h-4 w-4" />
                  </Button>
                  <Button variant={viewMode === 'grid' ? 'default' : 'ghost'} size="sm" onClick={() => setViewMode('grid')}>
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {tests.data.length > 0 ? (
          <>
            {viewMode === 'table' ? (
              renderTableView()
            ) : (
              <div className="mb-8 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {tests.data.map((test) => (
                  <Card key={test.id} className="flex flex-col">
                    {/* <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>Test {test.reference}</span>
                        <span className="rounded bg-green-100 px-2 py-1 text-sm font-normal text-green-800 dark:bg-green-900 dark:text-green-100">
                          {test.places_disponibles} places
                        </span>
                      </CardTitle>
                    </CardHeader> */}
                    <CardContent className="mt-4 flex-1">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2">
                          <Calendar className="text-muted-foreground h-5 w-5" />
                          <div>
                            <p className="font-medium">{format(parseISO(test.date), 'dd MMMM yyyy', { locale: fr })}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <MapPin className="text-muted-foreground h-5 w-5" />
                          <div>
                            <p className="font-medium">{test.lieu.nom}</p>
                            <p className="text-muted-foreground text-sm">
                              {test.lieu.adresse}, {test.lieu.ville.nom}, {test.lieu.ville.departement.nom}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Euro className="text-muted-foreground h-5 w-5" />
                          <p className="font-medium">{test.prix} €</p>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button className="w-full" asChild>
                        <Link href={route('reservation-test.show', test.id)}>Réserver</Link>
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}

            <div className="mt-8">
              <Pagination links={tests.links} />
            </div>
          </>
        ) : (
          <div className="py-12 text-center">
            <p className="text-muted-foreground text-lg">Aucun test psychotechnique disponible pour le moment.</p>
            <p className="mt-2">Veuillez modifier vos critères de recherche ou contactez-nous pour plus d'informations.</p>
          </div>
        )}

        <Card className="mt-12">
          <CardHeader>
            <CardTitle>Informations importantes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-2 font-semibold">À propos des tests psychotechniques</h3>
                <ul className="list-disc space-y-2 pl-5">
                  <li>Les tests durent environ 1 heure</li>
                  <li>Ils sont obligatoires pour récupérer votre permis après une suspension ou annulation</li>
                  <li>Ils évaluent vos aptitudes cognitives et comportementales à la conduite</li>
                  <li>Une attestation est délivrée à la fin du test</li>
                  <li>Les résultats sont transmis à la préfecture</li>
                </ul>
              </div>

              <div>
                <h3 className="mb-2 font-semibold">Documents à apporter</h3>
                <ul className="list-disc space-y-2 pl-5">
                  <li>Convocation au test</li>
                  <li>Pièce d'identité en cours de validité</li>
                  <li>Document du tribunal ou de la préfecture</li>
                  <li>Référence de paiement</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </FrontLayout>
  );
}
