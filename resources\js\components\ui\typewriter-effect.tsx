import * as React from "react";

interface Word {
  text: string;
  className?: string;
}

interface TypewriterEffectProps {
  words: Word[];
  className?: string;
  onWordChange?: (index: number) => void;
}

export function TypewriterEffect({ words, className = "", onWordChange }: TypewriterEffectProps) {
  const [displayedText, setDisplayedText] = React.useState("");
  const [wordIndex, setWordIndex] = React.useState(0);
  const [charIndex, setCharIndex] = React.useState(0);
  const [isDeleting, setIsDeleting] = React.useState(false);

  React.useEffect(() => {
    const currentWord = words[wordIndex].text;
    let timeout: NodeJS.Timeout;

    if (!isDeleting && charIndex <= currentWord.length) {
      setDisplayedText(currentWord.substring(0, charIndex));
      timeout = setTimeout(() => setCharIndex(charIndex + 1), 100);
    } else if (isDeleting && charIndex >= 0) {
      setDisplayedText(currentWord.substring(0, charIndex));
      timeout = setTimeout(() => setCharIndex(charIndex - 1), 50);
    } else if (!isDeleting && charIndex > currentWord.length) {
      timeout = setTimeout(() => setIsDeleting(true), 1000);
    } else if (isDeleting && charIndex < 0) {
      setIsDeleting(false);
      const nextIndex = (wordIndex + 1) % words.length;
      setWordIndex(nextIndex);

      // Utiliser un setTimeout pour éviter de mettre à jour l'état pendant le rendu
      if (onWordChange) {
        setTimeout(() => {
          onWordChange(nextIndex);
        }, 0);
      }
      setCharIndex(0);
    }

    return () => clearTimeout(timeout);
  }, [charIndex, isDeleting, wordIndex, words, onWordChange]);

  return (
    <h1 className={`text-3xl font-bold md:text-5xl ${className}`}>
      {words[wordIndex].className ? (
        <span className={words[wordIndex].className}>{displayedText}</span>
      ) : (
        displayedText
      )}
      <span className="animate-pulse">|</span>
    </h1>
  );
}
