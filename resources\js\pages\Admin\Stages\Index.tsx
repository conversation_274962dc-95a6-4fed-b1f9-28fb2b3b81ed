import { Head, router } from '@inertiajs/react';
import { PageProps, Stage, Lieu, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import TabNavigation from '@/components/TabNavigation';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

interface StagesPageProps extends PageProps {
    stages: PaginatedData<Stage>;
    lieux: Lieu[];
    isArchive: boolean;
}

export default function Index({ stages, lieux, isArchive = false }: StagesPageProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isImportOpen, setIsImportOpen] = useState(false);
    const [editingStage, setEditingStage] = useState<Stage | null>(null);

    const form = useForm({
        date_debut: '',
        date_fin: '',
        lieu_id: '',
        places_disponibles: '',
        prix: '',
        reference: '',
    });

    const importForm = useForm({
        file: null as File | null,
    });

    const columns = [
        {
            key: 'reference',
            label: 'Référence'
        },
        {
            key: 'date_debut',
            label: 'Date de début',
            render: (value: unknown) => {
                if (typeof value === 'string') {
                    return format(parseISO(value), 'dd/MM/yyyy', { locale: fr });
                }
                return '';
            }
        },
        {
            key: 'date_fin',
            label: 'Date de fin',
            render: (value: unknown) => {
                if (typeof value === 'string') {
                    return format(parseISO(value), 'dd/MM/yyyy', { locale: fr });
                }
                return '';
            }
        },
        {
            key: 'lieu',
            label: 'Lieu',
            render: (_value: unknown, row: Record<string, unknown>) => ((row as unknown) as Stage).lieu?.nom || ''
        },
        {
            key: 'places_disponibles',
            label: 'Places disponibles'
        },
        {
            key: 'prix',
            label: 'Prix',
            render: (value: unknown) => `${value} €`
        },
        {
            key: 'reservations_count',
            label: 'Réservations',
            render: (value: unknown) => Array.isArray(value) ? value.length : 0,
        },
    ];

    const handleAdd = () => {
        setEditingStage(null);
        form.reset();
        setIsOpen(true);
    };

    const handleImport = () => {
        importForm.reset();
        setIsImportOpen(true);
    };

    const handleImportSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        importForm.post(route('admin.stages.import'), {
            forceFormData: true,
            onSuccess: () => {
                setIsImportOpen(false);
                // toast.success('Stages importés avec succès');
            },
            onError: (errors) => {
                console.log(errors);
                toast.error('Erreur lors de l\'importation : ' + (errors.file || errors.message || 'Erreur inconnue'));
            }
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            importForm.setData('file', e.target.files[0]);
        }
    };

    const handleEdit = (row: Record<string, unknown>) => {
        const stage = row as unknown as Stage;
        console.log(stage)
        setEditingStage(stage);

        // Vérifier que les propriétés existent avant d'appeler toString()
        const lieu_id = stage.lieu?.id !== undefined ? stage.lieu?.id.toString() : '';
        const places_disponibles = stage.places_disponibles !== undefined ? stage.places_disponibles.toString() : '';
        const prix = stage.prix !== undefined ? stage.prix.toString() : '';

        form.setData({
            date_debut: stage.date_debut || '',
            date_fin: stage.date_fin || '',
            lieu_id: lieu_id,
            places_disponibles: places_disponibles,
            prix: prix,
            reference: stage.reference || '',
        });
        setIsOpen(true);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingStage) {
            form.put(route('admin.stages.update', editingStage.id), {
                onSuccess: () => setIsOpen(false),
            });
        } else {
            form.post(route('admin.stages.store'), {
                onSuccess: () => setIsOpen(false),
            });
        }
    };

    const handleDelete = (row: Record<string, unknown>) => {
        const stage = row as unknown as Stage;
        if (confirm('Êtes-vous sûr de vouloir supprimer ce stage ?')) {
            router.delete(route('admin.stages.destroy', stage.id));
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Stages',
            href: '/admin/stages',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Stages" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <TabNavigation
                    tabs={[
                        {
                            name: 'Stages à venir',
                            href: route('admin.stages.index'),
                            current: !isArchive,
                        },
                        {
                            name: 'Archives',
                            href: route('admin.stages.archive'),
                            current: isArchive,
                        },
                    ]}
                />
                <DataTable
                    title="Stages"
                    columns={columns}
                    data={stages.data.map(stage => ({
                        id: stage.id,
                        reference: stage.reference,
                        date_debut: stage.date_debut,
                        date_fin: stage.date_fin,
                        lieu: stage.lieu,
                        places_disponibles: stage.places_disponibles,
                        prix: stage.prix,
                        reservations: stage.reservations,
                    }))}
                    onAdd={handleAdd}
                    onImport={handleImport}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    pagination={{
                        links: stages.links,
                        from: stages.from,
                        to: stages.to,
                        total: stages.total
                    }}
                />
            </div>

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[900px]">
                    <DialogHeader>
                        <DialogTitle>
                            {editingStage ? 'Modifier le stage' : 'Ajouter un stage'}
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <Input
                                placeholder="Référence du stage"
                                value={form.data.reference}
                                onChange={e => form.setData('reference', e.target.value)}
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium mb-1">Date de début</label>
                                <Input
                                    type="date"
                                    value={form.data.date_debut}
                                    onChange={e => form.setData('date_debut', e.target.value)}
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium mb-1">Date de fin</label>
                                <Input
                                    type="date"
                                    value={form.data.date_fin}
                                    onChange={e => form.setData('date_fin', e.target.value)}
                                />
                            </div>
                        </div>
                        <div>
                            <Select
                                value={form.data.lieu_id}
                                onValueChange={(value) => form.setData('lieu_id', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Sélectionner un lieu" />
                                </SelectTrigger>
                                <SelectContent>
                                    {lieux.map((lieu) => (
                                        <SelectItem
                                            key={lieu.id}
                                            value={lieu.id.toString()}
                                        >
                                            {lieu.nom} - {lieu.ville?.nom}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Input
                                    type="number"
                                    placeholder="Places disponibles"
                                    value={form.data.places_disponibles}
                                    onChange={e => form.setData('places_disponibles', e.target.value)}
                                    min="1"
                                />
                            </div>
                            <div>
                                <Input
                                    type="number"
                                    placeholder="Prix (€)"
                                    value={form.data.prix}
                                    onChange={e => form.setData('prix', e.target.value)}
                                    min="0"
                                    step="0.01"
                                />
                            </div>
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsOpen(false)}
                            >
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingStage ? 'Mettre à jour' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            Importer des stages
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleImportSubmit} className="space-y-4">
                        <div>
                            <Label htmlFor="file">Fichier Excel</Label>
                            <Input
                                id="file"
                                type="file"
                                accept=".xlsx,.xls"
                                onChange={handleFileChange}
                                className="mt-1"
                            />
                            {importForm.errors.file && (
                                <p className="text-sm text-red-500 mt-1">{importForm.errors.file}</p>
                            )}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                            <p>Le fichier doit contenir les colonnes suivantes :</p>
                            <ul className="list-disc pl-5 mt-2">
                                <li>dates (format: DD/MM/YYYY)</li>
                                <li>prix</li>
                                <li>places_disponibles</li>
                                <li>lieu_id</li>
                            </ul>
                            <p className="mt-2">La date de fin sera automatiquement calculée en ajoutant un jour à la date de début.</p>
                            <p className="mt-2">
                                Veuillez utiliser un fichier Excel (.xlsx ou .xls) avec les colonnes indiquées ci-dessus.
                            </p>
                            <p className="mt-2">
                                <strong>Note:</strong> Les dates peuvent être au format texte (JJ/MM/AAAA) ou au format numérique Excel.
                            </p>
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsImportOpen(false)}
                            >
                                Annuler
                            </Button>
                            <Button type="submit" disabled={importForm.processing}>
                                Importer
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
