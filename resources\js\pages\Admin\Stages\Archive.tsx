import { Head, router } from '@inertiajs/react';
import { PageProps, Stage, Lieu, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import TabNavigation from '@/components/TabNavigation';

interface StagesArchivePageProps extends PageProps {
    stages: PaginatedData<Stage>;
    lieux: Lieu[];
    isArchive: boolean;
}

export default function Archive({ stages, lieux, isArchive }: StagesArchivePageProps) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [stageToDelete, setStageToDelete] = useState<Stage | null>(null);

    const breadcrumbItems: BreadcrumbItem[] = [
        { title: 'Stages', href: route('admin.stages.index') },
        { title: 'Archives', href: route('admin.stages.archive') },
    ];

    const columns = [
        { key: 'reference', label: 'Référence' },
        {
            key: 'date_debut',
            label: 'Date de début',
            render: (value: unknown): React.ReactNode => {
                return typeof value === 'string' ? new Date(value).toLocaleDateString('fr-FR') : '';
            },
        },
        {
            key: 'date_fin',
            label: 'Date de fin',
            render: (value: unknown): React.ReactNode => {
                return typeof value === 'string' ? new Date(value).toLocaleDateString('fr-FR') : '';
            },
        },
        {
            key: 'lieu',
            label: 'Lieu',
            render: (value: unknown): React.ReactNode => {
                const lieu = value as Lieu;
                return lieu ? `${lieu.nom} (${lieu.ville?.nom})` : '';
            },
        },
        { key: 'places_disponibles', label: 'Places disponibles' },
        {
            key: 'prix',
            label: 'Prix',
            render: (value: unknown): React.ReactNode => {
                return `${value} €`;
            },
        },
        {
            key: 'reservations',
            label: 'Réservations',
            render: (value: unknown): React.ReactNode => {
                return (Array.isArray(value) ? value.length : 0).toString();
            },
        },
    ];

    const handleDelete = (row: Record<string, unknown>) => {
        const stage = row as unknown as Stage;
        setStageToDelete(stage);
        setIsDeleteDialogOpen(true);
    };

    const confirmDelete = () => {
        if (stageToDelete) {
            router.delete(route('admin.stages.destroy', stageToDelete.id), {
                onSuccess: () => {
                    setIsDeleteDialogOpen(false);
                    setStageToDelete(null);
                },
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <Head title="Archives des stages" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <TabNavigation
                    tabs={[
                        {
                            name: 'Stages à venir',
                            href: route('admin.stages.index'),
                            current: !isArchive,
                        },
                        {
                            name: 'Archives',
                            href: route('admin.stages.archive'),
                            current: isArchive,
                        },
                    ]}
                />

                <DataTable
                    title="Archives des stages"
                    columns={columns}
                    data={stages.data.map(stage => ({
                        id: stage.id,
                        reference: stage.reference,
                        date_debut: stage.date_debut,
                        date_fin: stage.date_fin,
                        lieu: stage.lieu,
                        places_disponibles: stage.places_disponibles,
                        prix: stage.prix,
                        reservations: stage.reservations,
                    }))}
                    onDelete={handleDelete}
                    pagination={{
                        links: stages.links,
                        from: stages.from,
                        to: stages.to,
                        total: stages.total
                    }}
                />
            </div>

            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Confirmer la suppression</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        Êtes-vous sûr de vouloir supprimer ce stage ? Cette action est irréversible.
                    </div>
                    <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Annuler
                        </Button>
                        <Button variant="destructive" className='text-white' onClick={confirmDelete}>
                            Supprimer
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}


