import * as React from "react"
import { cn } from "@/lib/utils"
import { ChevronRight } from "lucide-react"

/**
 * Hero Section Component with Safari Compatibility
 *
 * This component includes browser detection to handle Safari-specific rendering issues:
 * - Hides complex RetroGrid animations that may cause performance issues in Safari
 * - Uses simplified gradients instead of complex radial gradients for Safari
 * - Provides Safari-friendly CTA button without spinning animations
 * - Maintains visual appeal across all browsers while ensuring compatibility
 */

// Browser detection hook
const useBrowserDetection = () => {
  const [isSafari, setIsSafari] = React.useState(false)
  const [isWebKit, setIsWebKit] = React.useState(false)

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const userAgent = window.navigator.userAgent.toLowerCase()

        // Detect Safari (including mobile Safari) without using deprecated vendor property
        const safariDetected = (
          // Standard Safari detection
          userAgent.includes('safari') &&
          !userAgent.includes('chrome') &&
          !userAgent.includes('chromium') &&
          !userAgent.includes('edge') &&
          !userAgent.includes('firefox')
        ) || (
          // Mobile Safari detection
          userAgent.includes('mobile') && userAgent.includes('safari') && !userAgent.includes('chrome')
        ) || (
          // Additional Safari detection patterns
          /version\/[\d.]+.*safari/i.test(userAgent) && !/chrome|chromium|edge|firefox/i.test(userAgent)
        )

        // Detect WebKit-based browsers that might have similar rendering issues
        const webkitDetected = (
          userAgent.includes('webkit') &&
          !userAgent.includes('chrome') &&
          !userAgent.includes('chromium') &&
          !userAgent.includes('edge')
        )

        setIsSafari(safariDetected)
        setIsWebKit(webkitDetected)
      } catch (error) {
        // Fallback: assume non-Safari if detection fails
        console.warn('Browser detection failed:', error)
        setIsSafari(false)
        setIsWebKit(false)
      }
    }
  }, [])

  return { isSafari, isWebKit }
}

interface HeroSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  subtitle?: {
    regular: string
    gradient: string
  }
  description?: string
  ctaText?: string
  ctaHref?: string
  bottomImage?: {
    light: string
    dark: string
  }
  gridOptions?: {
    angle?: number
    cellSize?: number
    opacity?: number
    lightLineColor?: string
    darkLineColor?: string
  }
}

const RetroGrid = ({
  angle = 65,
  cellSize = 60,
  opacity = 0.5,
  lightLineColor = "gray",
  darkLineColor = "gray",
  hiddenInSafari = false,
}) => {
  // Don't render the grid if it should be hidden in Safari
  if (hiddenInSafari) {
    return null
  }

  const gridStyles = {
    "--grid-angle": `${angle}deg`,
    "--cell-size": `${cellSize}px`,
    "--opacity": opacity,
    "--light-line": lightLineColor,
    "--dark-line": darkLineColor,
  } as React.CSSProperties

  return (
    <div
      className={cn(
        "pointer-events-none absolute size-full overflow-hidden [perspective:200px]",
        `opacity-[var(--opacity)]`,
      )}
      style={gridStyles}
    >
      <div className="absolute inset-0 [transform:rotateX(var(--grid-angle))]">
        <div className="animate-grid [background-image:linear-gradient(to_right,var(--light-line)_1px,transparent_0),linear-gradient(to_bottom,var(--light-line)_1px,transparent_0)] [background-repeat:repeat] [background-size:var(--cell-size)_var(--cell-size)] [height:300vh] [inset:0%_0px] [margin-left:-200%] [transform-origin:100%_0_0] [width:600vw] dark:[background-image:linear-gradient(to_right,var(--dark-line)_1px,transparent_0),linear-gradient(to_bottom,var(--dark-line)_1px,transparent_0)]" />
      </div>
      <div className="absolute inset-0 bg-gradient-to-t from-white to-transparent to-90% dark:from-black" />
    </div>
  )
}

const HeroSection = React.forwardRef<HTMLDivElement, HeroSectionProps>(
  (
    {
      className,
      title ,
      subtitle = {
        regular: "Designing your projects faster with ",
        gradient: "the largest figma UI kit.",
      },
      description = "",
      ctaText,
      ctaHref,
      bottomImage,
      gridOptions,
      ...props
    },
    ref,
  ) => {
    const { isSafari, isWebKit } = useBrowserDetection()

    return (
      <div className={cn("relative", className)} ref={ref} {...props}>
        <div
          className={cn(
            "absolute top-0 z-[0] h-80 w-screen",
            // Safari-friendly background
            isSafari || isWebKit
              ? "bg-gradient-to-b from-purple-950/20 via-purple-950/10 to-transparent dark:from-purple-950/30 dark:via-purple-950/15 dark:to-transparent"
              : "bg-purple-950/10 dark:bg-purple-950/10 bg-[radial-gradient(ellipse_20%_80%_at_50%_-20%,rgba(182,0,98,0.15),rgba(255,255,255,0))] dark:bg-[radial-gradient(ellipse_20%_80%_at_50%_-20%,rgba(182,0,98,0.3),rgba(255,255,255,0))]"
          )}
        />
        <section className="relative max-w-full mx-auto z-1">
          <RetroGrid {...gridOptions} hiddenInSafari={isSafari || isWebKit} />
          <div className="max-w-screen-xl z-10 mx-auto px-4 py-28 gap-12 md:px-8">
            <div className="space-y-5 max-w-3xl leading-0 lg:leading-5 mx-auto text-center">
              {title ?
              (<h1 className="text-sm text-[#b60062] dark:text-[#b60062] group font-geist mx-auto px-5 py-2 bg-gradient-to-tr from-zinc-300/20 via-gray-400/20 to-transparent dark:from-zinc-300/5 dark:via-gray-400/5 border-[2px] border-[#b60062]/5 dark:border-[#b60062]/5 rounded-3xl w-fit">
                {title}
                <ChevronRight className="inline w-4 h-4 ml-2 group-hover:translate-x-1 duration-300" />
              </h1>)
              : null}
              {subtitle.regular?
              <h2 className="text-3xl text-transparent dark:text-[#d3a4bd] tracking-tighter font-geist bg-clip-text mx-auto md:text-5xl bg-[linear-gradient(180deg,_#b60062_0%,_rgba(182,0,98,0.75)_100%)] dark:bg-[linear-gradient(180deg,_#b60062_0%,_rgba(182,0,98,0.75)_202.08%)]">
                {subtitle.regular}
              </h2>
              : null}

              {subtitle.gradient ?
              <h3 className="text-xl tracking-tighter font-geist bg-clip-text text-transparent mx-auto md:text-2xl bg-[linear-gradient(180deg,_#b60062_0%,_rgba(182,0,98,0.75)_100%)] dark:bg-[linear-gradient(180deg,_#b60062_0%,_rgba(182,0,98,0.75)_202.08%)]">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#b60062] to-[#18000d] dark:from-[#b60062] dark:to-[#d3a4bd]">
                  {subtitle.gradient}
                </span>
              </h3>
              : null}

              {description && (
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300">
                  {description}
                </p>
              )}
              {ctaText && ctaHref && (
                <div className="items-center justify-center gap-x-3 space-y-3 sm:flex sm:space-y-0">
                  {isSafari || isWebKit ? (
                    // Safari-friendly CTA button without complex animations
                    <a
                      href={ctaHref}
                      className="inline-flex rounded-full text-center group items-center justify-center bg-gradient-to-tr from-zinc-300/20 via-[#b60062]/30 to-transparent dark:from-zinc-300/5 dark:via-[#b60062]/20 text-gray-900 dark:text-white border-2 border-[#b60062]/20 hover:border-[#b60062]/40 hover:bg-gradient-to-tr hover:from-zinc-300/30 hover:via-[#b60062]/40 hover:to-transparent dark:hover:from-zinc-300/10 dark:hover:via-[#b60062]/30 transition-all py-4 px-10"
                    >
                      {ctaText}
                    </a>
                  ) : (
                    // Full-featured CTA button with animations for other browsers
                    <span className="relative inline-block overflow-hidden rounded-full p-[1.5px]">
                      <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#b60062_0%,#b60062_50%,#b60062_100%)]" />
                      <div className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-white dark:bg-gray-950 text-xs font-medium backdrop-blur-3xl">
                        <a
                          href={ctaHref}
                          className="inline-flex rounded-full text-center group items-center w-full justify-center bg-gradient-to-tr from-zinc-300/20 via-[#b60062]/30 to-transparent dark:from-zinc-300/5 dark:via-[#b60062]/20 text-gray-900 dark:text-white border-input border-[1px] hover:bg-gradient-to-tr hover:from-zinc-300/30 hover:via-[#b60062]/40 hover:to-transparent dark:hover:from-zinc-300/10 dark:hover:via-[#b60062]/30 transition-all sm:w-auto py-4 px-10"
                        >
                          {ctaText}
                        </a>
                      </div>
                    </span>
                  )}
                </div>
              )}
            </div>
            {bottomImage && (
              <div className="mt-32 mx-10 relative z-10">
                <img
                  src={bottomImage.light}
                  className="w-full shadow-lg rounded-lg border border-gray-200 dark:hidden"
                  alt="Dashboard preview"
                />
                <img
                  src={bottomImage.dark}
                  className="hidden w-full shadow-lg rounded-lg border border-gray-800 dark:block"
                  alt="Dashboard preview"
                />
              </div>
            )}
          </div>
        </section>
      </div>
    );
  },
);
HeroSection.displayName = "HeroSection"

export { HeroSection }

