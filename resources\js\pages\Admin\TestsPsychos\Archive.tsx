import { Head, router } from '@inertiajs/react';
import { PageProps, TestPsycho, Lieu, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import TabNavigation from '@/components/TabNavigation';

interface TestsPsychosArchivePageProps extends PageProps {
    tests: PaginatedData<TestPsycho>;
    lieux: Lieu[];
    isArchive: boolean;
}

export default function Archive({ tests, lieux, isArchive }: TestsPsychosArchivePageProps) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [testToDelete, setTestToDelete] = useState<TestPsycho | null>(null);

    const breadcrumbItems: BreadcrumbItem[] = [
        { title: 'Dashboard', href: route('admin.dashboard') },
        { title: 'Tests psychotechniques', href: route('admin.tests-psychos.index') },
        { title: 'Archives', href: route('admin.tests-psychos.archive') },
    ];

    const columns = [
        { key: 'reference', label: 'Référence' },
        {
            key: 'date',
            label: 'Date',
            render: (value: unknown): React.ReactNode => {
                return value ? new Date(value as string).toLocaleDateString('fr-FR') : '';
            },
        },
        {
            key: 'lieu',
            label: 'Lieu',
            render: (value: Lieu): React.ReactNode => {
                return value ? `${value.nom} (${value.ville?.nom})` : '';
            },
        },
        // {
        //     key: 'places_disponibles',
        //     label: 'Places disponibles',
        //     render: (value: unknown): React.ReactNode => {
        //         return <div className="text-center">{value as React.ReactNode}</div>;
        //     }
        // },
        {
            key: 'prix',
            label: 'Prix',
            render: (value: unknown): React.ReactNode => {
                return <div>{value as React.ReactNode}</div>;
            },
        },
        {
            key: 'reservations_count',
            label: 'Réservations',
            render: (value: unknown): React.ReactNode => {
                return <div>{value as React.ReactNode || 0}</div>;
            },
        },
    ];

    const handleDelete = (row: Record<string, unknown>) => {
        const test = row as unknown as TestPsycho;
        setTestToDelete(test);
        setIsDeleteDialogOpen(true);
    };

    const confirmDelete = () => {
        if (testToDelete) {
            router.delete(route('admin.tests-psychos.destroy', testToDelete.id), {
                onSuccess: () => {
                    setIsDeleteDialogOpen(false);
                    setTestToDelete(null);
                },
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <Head title="Archives des tests psychotechniques" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">

                <TabNavigation
                    tabs={[
                        {
                            name: 'Tests à venir',
                            href: route('admin.tests-psychos.index'),
                            current: !isArchive,
                        },
                        {
                            name: 'Archives',
                            href: route('admin.tests-psychos.archive'),
                            current: isArchive,
                        },
                    ]}
                />

                <DataTable
                    title="Archives des tests psychotechniques"
                    columns={columns}
                    data={tests.data.map(test => ({
                        id: test.id,
                        reference: test.reference,
                        date: test.date,
                        heure: test.heure,
                        lieu: test.lieu,
                        places_disponibles: test.places_disponibles,
                        prix: test.prix,
                        reservations_count: test.reservations_count,
                    }))}
                    onDelete={handleDelete}
                    pagination={{
                        links: tests.links,
                        from: tests.from,
                        to: tests.to,
                        total: tests.total
                    }}
                />
            </div>

            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Confirmer la suppression</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        Êtes-vous sûr de vouloir supprimer ce test psychotechnique ? Cette action est irréversible.
                    </div>
                    <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Annuler
                        </Button>
                        <Button variant="destructive" onClick={confirmDelete}>
                            Supprimer
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}


