import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useState, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import FrontLayout from '@/layouts/front-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Calendar, MapPin, Euro, User, AlertCircle } from 'lucide-react';
import InputError from '@/components/input-error';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface PaiementProps {
  reservation: {
    id: number;
    statut: string;
    date_reservation: string;
    cas: string;
    stage: {
      id: number;
      reference: string;
      date_debut: string;
      date_fin: string;
      prix: number;
      lieu: {
        nom: string;
        ville: {
          nom: string;
          departement: {
            nom: string;
          };
        };
      };
    };
    user: {
      id: number;
      nom: string;
      prenom: string;
      email: string;
    };
  };
}

export default function Paiement({ reservation }: PaiementProps) {
  const [paymentMethod, setPaymentMethod] = useState<string>('paypal');
  const [captchaValue, setCaptchaValue] = useState<string>('');
  const [captchaInput, setCaptchaInput] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const { setData, post, processing, errors } = useForm({
    payment_method: 'paypal',
  });

  // Générer un captcha aléatoire lors du chargement du composant
  useEffect(() => {
    const randomString = generateRandomString(8);
    setCaptchaValue(randomString);
  }, []);

  const generateRandomString = (length: number) => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  };

  const handlePaymentMethodChange = (value: string) => {
    setPaymentMethod(value);
    setData('payment_method', value);

    // Réinitialiser les erreurs
    setError(null);
  };

  const handleCaptchaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCaptchaInput(e.target.value);
  };

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    // Vérifier le captcha pour les méthodes autres que PayPal
    if (['virement', 'cheque', 'bon'].includes(paymentMethod) && captchaInput !== captchaValue) {
      setError('Le code de vérification est incorrect.');
      return;
    }

    if (paymentMethod === 'paypal') {
      // Pour PayPal, utiliser une redirection traditionnelle au lieu d'une requête AJAX
      // Créer un formulaire HTML standard et le soumettre
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = route('paiement.process', reservation.id);

      // Ajouter le token CSRF
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
      if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
      }

      // Ajouter la méthode de paiement
      const methodInput = document.createElement('input');
      methodInput.type = 'hidden';
      methodInput.name = 'payment_method';
      methodInput.value = 'paypal';
      form.appendChild(methodInput);

      // Ajouter le formulaire au DOM, le soumettre et le supprimer
      document.body.appendChild(form);
      form.submit();
    } else {
      // Pour les autres méthodes, utiliser Inertia
      post(route('paiement.process', reservation.id));
    }
  };

  return (
    <FrontLayout title="Paiement">
      <Head title="Paiement" />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
              <span className="text-sm">✓</span>
            </div>
            <span className="font-medium">Authentification</span>
          </div>
          <div className="mx-2 h-px w-8 bg-gray-300"></div>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">2</div>
            <span className="font-medium">Paiement</span>
          </div>
          <div className="mx-2 h-px w-8 bg-gray-300"></div>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-gray-700">3</div>
            <span className="text-gray-700">Confirmation</span>
          </div>
        </div>

        <div className="grid gap-8 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Paiement sécurisé</CardTitle>
                <CardDescription>
                  Veuillez saisir vos informations de paiement pour finaliser votre réservation
                </CardDescription>
              </CardHeader>
              <CardContent>
                {error && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <form onSubmit={submit} className="space-y-6" id="payment-form">
                  <div className="space-y-4">
                    <div>
                      <Label className="mb-2 block font-medium">Choisissez votre méthode de paiement</Label>
                      <RadioGroup
                        value={paymentMethod}
                        onValueChange={handlePaymentMethodChange}
                        className="space-y-4"
                      >


                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="paypal" id="payment-paypal" />
                          <Label htmlFor="payment-paypal" className="flex-1 cursor-pointer">
                            <div className="flex items-center gap-2">
                              <img src="/paypal.webp" alt="PayPal" className="h-5 w-auto" />
                              <span>PayPal</span>
                            </div>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="sumup" id="payment-sumup" />
                          <Label htmlFor="payment-sumup" className="flex-1 cursor-pointer">
                            <div className="flex items-center gap-2">
                              <img src="/sumup.webp" alt="SumUp" className="h-5 w-auto" />
                              <span>Carte bancaire (SumUp)</span>
                            </div>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="virement" id="payment-virement" />
                          <Label htmlFor="payment-virement" className="flex-1 cursor-pointer">
                            <span>Virement bancaire</span>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="cheque" id="payment-cheque" />
                          <Label htmlFor="payment-cheque" className="flex-1 cursor-pointer">
                            <span>Paiement par chèque</span>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="bon" id="payment-bon" />
                          <Label htmlFor="payment-bon" className="flex-1 cursor-pointer">
                            <span>Paiement par Bon</span>
                          </Label>
                        </div>
                      </RadioGroup>
                      <InputError message={errors.payment_method} />
                    </div>



                    {paymentMethod === 'paypal' && (
                      <div className="rounded-md bg-blue-50 p-4 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                        <p className="mb-2">
                          Vous allez être redirigé vers PayPal pour effectuer votre paiement en toute sécurité.
                        </p>
                        <p className="text-sm">
                          PayPal vous permet de payer avec votre compte PayPal ou avec votre carte bancaire sans avoir à créer un compte.
                        </p>
                      </div>
                    )}

                    {paymentMethod === 'sumup' && (
                      <div className="rounded-md bg-green-50 p-4 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                        <p className="mb-2">
                          Vous allez être redirigé vers SumUp pour effectuer votre paiement par carte bancaire en toute sécurité.
                        </p>
                        <p className="text-sm">
                          SumUp accepte toutes les cartes bancaires (Visa, Mastercard, American Express, etc.) et ne nécessite pas de création de compte.
                        </p>
                      </div>
                    )}

                    {['virement', 'cheque', 'bon'].includes(paymentMethod) && (
                      <>
                        <div className="rounded-md bg-amber-50 p-4 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300">
                          <p className="mb-2">
                            {paymentMethod === 'virement' && 'Les informations pour effectuer le virement vous seront communiquées par email.'}
                            {paymentMethod === 'cheque' && 'Les informations pour envoyer votre chèque vous seront communiquées par email.'}
                            {paymentMethod === 'bon' && 'Les informations pour utiliser votre bon vous seront communiquées par email.'}
                          </p>
                          <p className="text-sm">
                            Votre place sera réservée pendant 7 jours, le temps de recevoir votre paiement.
                          </p>
                        </div>

                        <div>
                          <Label htmlFor="captcha">Entrez le code de vérification pour activer le bouton</Label>
                          <div className="mt-2 flex flex-col gap-2">
                            <div className="rounded-md bg-gray-100 p-2 text-center font-mono text-lg font-bold dark:bg-gray-800">
                              {captchaValue}
                            </div>
                            <Input
                              id="captcha"
                              value={captchaInput}
                              onChange={handleCaptchaChange}
                              placeholder="Entrez le code ci-dessus"
                            />
                          </div>
                        </div>
                      </>
                    )}
                  </div>

                  <div className="flex justify-between">
                    <Button type="button" variant="outline" onClick={() => window.history.back()}>
                      Retour
                    </Button>
                    <Button
                      type="submit"
                      disabled={
                        processing ||
                        (['virement', 'cheque', 'bon'].includes(paymentMethod) && captchaInput !== captchaValue)
                      }
                    >
                      {paymentMethod === 'paypal' ? 'Continuer vers PayPal' :
                       paymentMethod === 'sumup' ? 'Continuer vers SumUp' :
                       `Payer ${reservation.stage.prix} €`}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Récapitulatif</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">
                      {format(parseISO(reservation.stage.date_debut), 'dd MMMM yyyy', { locale: fr })}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      au {format(parseISO(reservation.stage.date_fin), 'dd MMMM yyyy', { locale: fr })}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{reservation.stage.lieu.nom}</p>
                    <p className="text-sm text-muted-foreground">
                      {reservation.stage.lieu.ville.nom}, {reservation.stage.lieu.ville.departement.nom}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Euro className="h-5 w-5 text-muted-foreground" />
                  <p className="font-medium">{reservation.stage.prix} €</p>
                </div>

                <Separator />

                <div className="flex items-center gap-2">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{reservation.user.prenom} {reservation.user.nom}</p>
                    <p className="text-sm text-muted-foreground">{reservation.user.email}</p>
                  </div>
                </div>

                <div className="rounded-md bg-muted p-4">
                  <p className="text-sm">
                    <span className="font-medium">Type de stage:</span>{' '}
                    {reservation.cas === '1' && 'Récupération volontaire de 4 points'}
                    {reservation.cas === '2' && 'Stage en période probatoire'}
                    {reservation.cas === '3' && 'Alternative aux poursuites ou composition pénale'}
                    {reservation.cas === '4' && 'Peine complémentaire ou mise à l\'épreuve'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </FrontLayout>
  );
}
