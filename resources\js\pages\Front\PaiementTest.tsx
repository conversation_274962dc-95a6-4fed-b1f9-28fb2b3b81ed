import FrontLayout from '@/layouts/front-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useForm } from '@inertiajs/react';
import { useState, useEffect, FormEventHandler } from 'react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Head } from '@inertiajs/react';
import { ReservationTestPsycho } from '@/types';

interface PaiementTestProps {
  reservation: ReservationTestPsycho & {
    test_psycho: {
      prix: number;
      date: string;

      reference: string;
      lieu: {
        nom: string;
        ville: {
          nom: string;
          departement: {
            nom: string;
          };
        };
      };
    };
    type_test_psycho: {
      nom: string;
    };
  };
}

export default function PaiementTest({ reservation }: PaiementTestProps) {
  const [paymentMethod, setPaymentMethod] = useState<string>('paypal');
  const [captchaValue, setCaptchaValue] = useState<string>('');
  const [captchaInput, setCaptchaInput] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const { setData, post, processing } = useForm({
    payment_method: 'paypal',
  });

  // Générer un captcha aléatoire lors du chargement du composant
  useEffect(() => {
    const randomString = generateRandomString(8);
    setCaptchaValue(randomString);
  }, []);

  const generateRandomString = (length: number) => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  };

  const handlePaymentMethodChange = (value: string) => {
    setPaymentMethod(value);
    setData('payment_method', value);

    // Réinitialiser les erreurs
    setError(null);
  };

  const handleCaptchaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCaptchaInput(e.target.value);
  };

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    // Vérifier le captcha pour les méthodes autres que PayPal
    if (['virement', 'cheque', 'bon'].includes(paymentMethod) && captchaInput !== captchaValue) {
      setError('Le code de vérification est incorrect.');
      return;
    }

    if (paymentMethod === 'paypal') {
      // Pour PayPal, utiliser une redirection traditionnelle au lieu d'une requête AJAX
      // Créer un formulaire HTML standard et le soumettre
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = route('paiement-test.process', reservation.id);

      // Ajouter le token CSRF
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
      if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
      }

      // Ajouter la méthode de paiement
      const methodInput = document.createElement('input');
      methodInput.type = 'hidden';
      methodInput.name = 'payment_method';
      methodInput.value = 'paypal';
      form.appendChild(methodInput);

      // Ajouter le formulaire au DOM, le soumettre et le supprimer
      document.body.appendChild(form);
      form.submit();
    } else {
      // Pour les autres méthodes, utiliser Inertia
      post(route('paiement-test.process', reservation.id));
    }
  };

  return (
    <FrontLayout title="Paiement du test psychotechnique">
      <Head title="Paiement du test psychotechnique" />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
              <span className="text-sm">✓</span>
            </div>
            <span className="font-medium">Authentification</span>
          </div>
          <div className="mx-2 h-px w-8 bg-gray-300"></div>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">2</div>
            <span className="font-medium">Paiement</span>
          </div>
          <div className="mx-2 h-px w-8 bg-gray-300"></div>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-300 text-gray-600">3</div>
            <span className="font-medium">Confirmation</span>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Paiement</CardTitle>
              </CardHeader>
              <CardContent>
                {error && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <form onSubmit={submit} className="space-y-6" id="payment-form">
                  <div className="space-y-4">
                    <div>
                      <Label className="mb-2 block font-medium">Choisissez votre méthode de paiement</Label>
                      <RadioGroup
                        value={paymentMethod}
                        onValueChange={handlePaymentMethodChange}
                        className="space-y-4"
                      >
                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="paypal" id="payment-paypal" />
                          <Label htmlFor="payment-paypal" className="flex-1 cursor-pointer">
                            <div className="flex items-center gap-2">
                              <img src="/paypal.webp" alt="PayPal" className="h-5 w-auto" />
                              <span>PayPal</span>
                            </div>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="virement" id="payment-virement" />
                          <Label htmlFor="payment-virement" className="flex-1 cursor-pointer">
                            <span>Virement bancaire</span>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="cheque" id="payment-cheque" />
                          <Label htmlFor="payment-cheque" className="flex-1 cursor-pointer">
                            <span>Chèque</span>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted">
                          <RadioGroupItem value="bon" id="payment-bon" />
                          <Label htmlFor="payment-bon" className="flex-1 cursor-pointer">
                            <span>Bon de commande</span>
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    {['virement', 'cheque', 'bon'].includes(paymentMethod) && (
                      <div className="space-y-4 rounded-md border p-4">
                        <div>
                          <Label htmlFor="captcha" className="mb-2 block font-medium">
                            Veuillez saisir le code suivant : <span className="font-bold">{captchaValue}</span>
                          </Label>
                          <Input
                            id="captcha"
                            type="text"
                            value={captchaInput}
                            onChange={handleCaptchaChange}
                            placeholder="Saisissez le code"
                            className="max-w-xs"
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-between">
                    <Button type="button" variant="outline" onClick={() => window.history.back()}>
                      Retour
                    </Button>
                    <Button
                      type="submit"
                      disabled={
                        processing ||
                        (['virement', 'cheque', 'bon'].includes(paymentMethod) && captchaInput !== captchaValue)
                      }
                    >
                      {paymentMethod === 'paypal' ? 'Continuer vers PayPal' :
                       paymentMethod === 'sumup' ? 'Continuer vers SumUp' :
                       `Payer ${reservation.test_psycho.prix} €`}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Récapitulatif</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold">Type de test</h3>
                  <p>{reservation.type_test_psycho.nom}</p>
                </div>
                <div>
                  <h3 className="font-semibold">Date</h3>
                  <p>{format(parseISO(reservation.test_psycho.date), 'dd MMMM yyyy', { locale: fr })}</p>
                </div>

                <div>
                  <h3 className="font-semibold">Lieu</h3>
                  <p>{reservation.test_psycho.lieu.nom}</p>
                  <p className="text-sm text-muted-foreground">
                    {reservation.test_psycho.lieu.ville.nom}, {reservation.test_psycho.lieu.ville.departement.nom}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold">Prix</h3>
                  <p className="text-lg font-bold">{reservation.test_psycho.prix} €</p>
                </div>
                <div>
                  <h3 className="font-semibold">Référence</h3>
                  <p>{reservation.test_psycho.reference}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </FrontLayout>
  );
}
