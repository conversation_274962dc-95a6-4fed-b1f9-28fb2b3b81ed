<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Imports\StagesImport;
use App\Models\Stage;
use App\Models\Lieu;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class StageController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/Stages/Index', [
            'stages' => Stage::with('lieu.ville.departement')
                ->withCount('reservations')
                ->where('date_debut', '>=', now())
                ->paginate(10),
            'lieux' => Lieu::with('ville.departement')->get(),
            'isArchive' => false
        ]);
    }

    public function archive()
    {
        return Inertia::render('Admin/Stages/Archive', [
            'stages' => Stage::with('lieu.ville.departement')
                ->withCount('reservations')
                ->where('date_debut', '<', now())
                ->paginate(10),
            'lieux' => Lieu::with('ville.departement')->get(),
            'isArchive' => true
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'date_debut' => 'required|date|after:today',
            'date_fin' => 'required|date|after:date_debut',
            'lieu_id' => 'required|exists:lieus,id',
            'places_disponibles' => 'required|integer|min:1',
            'prix' => 'required|numeric|min:0',
            'reference' => 'required|string|unique:stages'
        ]);

        Stage::create($validated);

        return redirect()->back()->with('success', 'Stage créé avec succès.');
    }

    public function update(Request $request, Stage $stage)
    {
        $validated = $request->validate([
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after:date_debut',
            'lieu_id' => 'required|exists:lieus,id',
            'places_disponibles' => 'required|integer|min:1',
            'prix' => 'required|numeric|min:0',
            'reference' => 'required|string|unique:stages,reference,' . $stage->id
        ]);

        $stage->update($validated);

        return redirect()->back()->with('success', 'Stage mis à jour avec succès.');
    }

    public function destroy(Stage $stage)
    {
        $stage->delete();
        return redirect()->back()->with('success', 'Stage supprimé avec succès.');
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file',
        ], [
            'file.required' => 'Veuillez sélectionner un fichier',
            'file.file' => 'Le fichier est invalide',
        ]);

        // Vérification manuelle du type de fichier
        $extension = $request->file('file')->getClientOriginalExtension();
        if (!in_array(strtolower($extension), ['xlsx', 'xls'])) {
            return redirect()->back()->with('error', 'Le fichier doit être au format Excel (xlsx, xls)');
        }

        try {
            // Enregistrer le fichier temporairement pour le déboguer si nécessaire
            $path = $request->file('file')->store('temp');
            Log::info('Fichier importé: ' . $path);

            // Importer les données
            Excel::import(new StagesImport, $request->file('file'));

            return redirect()->back()->with('success', 'Stages importés avec succès.');
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            // Erreurs de validation
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = "Ligne {$failure->row()}: {$failure->errors()[0]}";
            }

            return redirect()->back()->with('error', 'Erreurs de validation: ' . implode(', ', $errors));
        } catch (\Exception $e) {
            // Autres erreurs
            Log::error('Erreur d\'importation: ' . $e->getMessage(), [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // Message d'erreur plus convivial
            $errorMessage = 'Erreur lors de l\'importation : ';

            if (strpos($e->getMessage(), 'Format de date non reconnu') !== false) {
                $errorMessage .= 'Le format de date n\'est pas reconnu. Assurez-vous que les dates sont au format JJ/MM/AAAA ou au format Excel.';
            } else {
                $errorMessage .= $e->getMessage();
            }

            return redirect()->back()->with('error', $errorMessage);
        }
    }
}
