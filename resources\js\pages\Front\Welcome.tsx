import FranceMap from '@/components/FranceMap';
import { Button } from '@/components/ui/button';
import { ColourfulText } from '@/components/ui/colourful-text';
import { NewsCarousel } from '@/components/ui/news-carousel';
import { TestimonialsFr } from '@/components/ui/testimonials-fr';
import { TypewriterEffect } from '@/components/ui/typewriter-effect';
import FrontLayout from '@/layouts/front-layout';
import { Departement } from '@/types';
import { Link } from '@inertiajs/react';
import { Award, Calendar, CheckCircle, FileText, Info, Mail, MapPin, Phone } from 'lucide-react';
import React from 'react';

interface WelcomeProps {
  departements: Departement[];
}

export default function Welcome({ departements = [] }: WelcomeProps) {
  const [currentWordIndex, setCurrentWordIndex] = React.useState(0);

  const backgroundImages = ['url(/slide1.jpg)', 'url(/slide2.jpg)'];

  return (
    <FrontLayout title="Accueil">
      {/* Hero */}
      <section className="relative flex flex-col items-center justify-center overflow-hidden bg-cover bg-center py-24 text-center text-white">
        {/* Background images with fade transition */}
        <div
          className="absolute inset-0 transition-opacity duration-2000 ease-in-out animate-fade-out"
          style={{
            backgroundImage: backgroundImages[0],
            opacity: currentWordIndex === 0 ? 1 : 0,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            transform: currentWordIndex === 0 ? 'scale(1.05)' : 'scale(1)',
            transition: 'opacity 1000ms ease-in-out, transform 4000ms ease-in-in',
          }}
        />
        <div
          className="absolute inset-0 transition-opacity duration-2000 ease-in-out animate-fade-in"
          style={{
            backgroundImage: backgroundImages[1],
            opacity: currentWordIndex === 1 ? 1 : 0,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            transform: currentWordIndex === 1 ? 'scale(1.05)' : 'scale(1)',
            transition: 'opacity 1000ms ease-in-out, transform 4000ms ease-in-out',
          }}
        />
        {/* Dark overlay with buildings background */}
        <div
          className="absolute inset-0 bg-black/5 dark:bg-black/50"
          //   style={{
          //     background: '#111 url(/images/buildings.png) repeat-x left bottom',
          //   }}
        />
        <div className="relative z-10 max-w-5xl px-4 ">
          <div className="p-10 mb-5 bg-black/10">
            <h1 className="font-heading relative z-2 text-center text-2xl font-bold uppercase md:text-5xl lg:text-6xl text-white/60">
              {currentWordIndex === 0 ? (
                <>
                  <span className="text-white font-['Raleway'] text-shadow-lg/30">STAGES DE SENSIBILISATION</span>
                  <br />
                  <ColourfulText text="A LA SECURITE ROUTIERE" />
                </>
              ) : (
                <>
                  <span className="text-white font-['Raleway'] text-shadow-lg">Des Tests </span>
                  <br /> <ColourfulText text="Psychotechniques" />
                </>
              )}
            </h1>
            {currentWordIndex === 0 ?
              <p className="mt-4 text-lg text-white p-3">Récupérez jusqu'à 4 points sur votre permis</p> :
              <p className="mt-4 text-lg text-white p-3">Réservez un test psychotechnique près de chez vous</p>
            }
            <TypewriterEffect
              className={'hidden text-xs'}
              words={[
                { text: 'PsychotechniquesPsychotechniquesPsychotechniquesPsychotechniques', className: 'text-xs hidden' },
                { text: 'PsychotechniquesPsychotechniquesPsychotechniquesPsychotechniques', className: 'text-xs hidden' },
              ]}
              onWordChange={(index) => setCurrentWordIndex(index)}
            />
          </div>
          <div className="mb-6 flex justify-center gap-4">
            <Button size="lg" className="bg-primary rounded-none hover:bg-primary/90 dark:bg-primary/90 dark:hover:bg-primary font-bold text-white" asChild>
              <Link href={route('stages')}>TROUVER UN STAGE</Link>
            </Button>
            <Button
              size="lg"
              className="text-primary border-primary border-1 rounded-none bg-white font-bold hover:bg-white/90 dark:bg-gray-200 dark:hover:bg-white"
              asChild
            >
              <Link href={route('tests-psychotechniques')}>TROUVER UN TEST</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Slogan */}
      <section className="bg-primary/5 dark:bg-primary/20 py-8 text-center">
        <div className="container mx-auto">
          <div className="flex flex-col items-center">
            <h2 className="mb-2 text-3xl font-bold uppercase">
              AVEC <span className="text-primary">STRIATUM</span>
            </h2>
            <h3 className="mb-4 text-xl font-light">
              <span className="text-primary">VOUS</span> FAITES LE POINT SUR VOTRE PERMIS
            </h3>
            <div className="relative mb-4 inline-block">
              <div className="bg-primary absolute left-1/2 h-px w-20 -translate-x-1/2"></div>
              <div className="border-primary absolute -top-1.5 left-1/2 h-4 w-4 -translate-x-1/2 rounded-full border-2 bg-white dark:bg-gray-900"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Stages and Tests */}
      <section className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Stages */}
          <div className="rounded-lg border border-gray-100 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800">
            <h2 className="text-primary dark:text-primary font-heading mb-4 flex items-center text-xl font-semibold uppercase">
              <MapPin className="mr-2 h-5 w-5" /> Sélectionnez votre département
            </h2>
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900">
              <FranceMap departements={departements} />
            </div>
            <p className="text-muted-foreground mt-4 text-sm dark:text-gray-300">
              Les départements en couleur disposent de stages à venir. Cliquez sur un département coloré pour voir les stages disponibles.
            </p>
          </div>

          {/* Tests Psychotechniques */}
          <div className="rounded-lg border border-gray-100 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800">
            <h2 className="text-primary dark:text-primary font-heading mb-4 text-center text-xl font-semibold uppercase">Actualités</h2>
            <NewsCarousel />
          </div>
        </div>
      </section>

      {/* Services */}
      <section className="bg-gray-50 py-12 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="mb-8 text-center">
            <h2 className="text-primary dark:text-primary font-heading mb-4 text-3xl font-bold uppercase">STRIATUM FORMATION</h2>
            <div className="bg-primary mx-auto mb-4 h-1 w-24"></div>
            <p className="text-muted-foreground mx-auto max-w-2xl text-center dark:text-gray-300">
              Striatum formation est une structure spécialisée dans la promotion de la santé avec des actions dans les trois niveaux de prévention
              (primaire, secondaire et tertiaire).
            </p>
          </div>

          <div className="rounded-lg border border-gray-100 bg-white p-6 shadow-lg dark:border-gray-700 dark:bg-gray-800">
            <h3 className="font-heading text-primary dark:text-primary mb-6 text-center text-xl font-semibold uppercase">NOUS INTERVENONS DANS :</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="flex flex-col items-center rounded-lg border p-4 transition-shadow hover:shadow-md">
                <div className="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
                  <Award className="text-primary h-8 w-8" />
                </div>
                <h4 className="text-primary dark:text-primary font-heading mb-2 text-lg font-semibold">Suivi psychologique</h4>
                <p className="text-muted-foreground text-center text-sm">Le suivi psychothérapeutique individuel et/ou collectif.</p>
              </div>

              <div className="flex flex-col items-center rounded-lg border p-4 transition-shadow hover:shadow-md">
                <div className="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
                  <FileText className="text-primary h-8 w-8" />
                </div>
                <h4 className="text-primary dark:text-primary font-heading mb-2 text-lg font-semibold">Risque routier</h4>
                <p className="text-muted-foreground text-center text-sm">L'évaluation du risque routier.</p>
              </div>

              <div className="flex flex-col items-center rounded-lg border p-4 transition-shadow hover:shadow-md">
                <div className="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
                  <Info className="text-primary h-8 w-8" />
                </div>
                <h4 className="text-primary dark:text-primary font-heading mb-2 text-lg font-semibold">Risques psychosociaux</h4>
                <p className="text-muted-foreground text-center text-sm">
                  Le repérage, l'évaluation et l'intervention en entreprise sur les risques psychosociaux.
                </p>
              </div>

              <div className="flex flex-col items-center rounded-lg border p-4 transition-shadow hover:shadow-md">
                <div className="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
                  <CheckCircle className="text-primary h-8 w-8" />
                </div>
                <h4 className="text-primary dark:text-primary font-heading mb-2 text-lg font-semibold">Compétences</h4>
                <p className="text-muted-foreground text-center text-sm">
                  Évaluation des compétences cognitives via des tests psychotechniques agréés.
                </p>
              </div>

              <div className="flex flex-col items-center rounded-lg border p-4 transition-shadow hover:shadow-md">
                <div className="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
                  <Calendar className="text-primary h-8 w-8" />
                </div>
                <h4 className="text-primary dark:text-primary font-heading mb-2 text-lg font-semibold">Organisation de stages</h4>
                <p className="text-muted-foreground text-center text-sm">Organisation de stages de sensibilisation à la sécurité routière.</p>
              </div>

              <div className="flex flex-col items-center rounded-lg border p-4 transition-shadow hover:shadow-md">
                <div className="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
                  <Phone className="text-primary h-8 w-8" />
                </div>
                <h4 className="text-primary dark:text-primary font-heading mb-2 text-lg font-semibold">PSSM</h4>
                <p className="text-muted-foreground text-center text-sm">Premiers secours santé mentale pour aider en cas de crise psychique.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="relative py-12">
        <div className="absolute inset-0 z-0">
          <img src="/images/road-bg.jpg" alt="Route" className="h-full w-full object-cover opacity-50 dark:opacity-30" />
          <div className="absolute inset-0 bg-black/30 dark:bg-black/50"></div>
        </div>
        <div className="relative z-10 container mx-auto px-4">
          <div className="mb-8 text-center">
            <h2 className="font-heading mb-4 text-3xl font-bold text-white uppercase">NOS CHIFFRES CLÉS</h2>
            <div className="mx-auto mb-4 h-1 w-24 bg-white"></div>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            {/* Satisfaction des clients */}
            <div className="flex flex-col rounded-lg bg-white/95 p-6 shadow-lg dark:bg-gray-800/95">
              <div className="mb-4 text-center">
                <h3 className="text-primary dark:text-primary font-heading text-2xl font-bold uppercase">SATISFACTION</h3>
                <h4 className="text-primary dark:text-primary font-heading text-xl font-bold uppercase">DES CLIENTS</h4>
              </div>
              <div className="mb-4">
                <img src="/images/educ.jpg" alt="Conductrice" className="h-48 w-full rounded-md object-cover" />
              </div>
              <p className="text-sm dark:text-gray-300">
                Les conducteurs apprécient notre capacité à trouver des solutions et recommandent le sérieux de notre service.
              </p>
            </div>

            {/* Centre - Statistiques */}
            <div className="bg-primary/95 dark:bg-primary/90 flex flex-col justify-between rounded-lg p-6 text-center text-white shadow-lg">
              <div className="mb-6 flex flex-col items-center">
                <p className="font-heading text-5xl font-bold">5,009</p>
                <p className="font-heading mt-2 text-lg uppercase">stages agréés</p>
              </div>
              <div className="mb-6 flex flex-col items-center">
                <p className="font-heading text-5xl font-bold">145,992</p>
                <p className="font-heading mt-2 text-lg uppercase">Heures de formation</p>
              </div>
              <div className="flex flex-col items-center">
                <p className="font-heading text-5xl font-bold">20,036</p>
                <p className="font-heading mt-2 text-lg uppercase">points récupérés</p>
              </div>
            </div>

            {/* Personnalisation des services */}
            <div className="flex flex-col rounded-lg bg-white/95 p-6 shadow-lg dark:bg-gray-800/95">
              <div className="mb-4 text-center">
                <h3 className="text-primary dark:text-primary font-heading text-2xl font-bold uppercase">PERSONNALISATION</h3>
                <h4 className="text-primary dark:text-primary font-heading text-xl font-bold uppercase">DES SERVICES</h4>
              </div>
              <p className="mb-4 text-sm dark:text-gray-300">
                Nos experts vous accompagnent dans la gestion de votre dossier jusqu'à la récupération de vos points.
              </p>
              <div className="mt-auto">
                <img src="/images/women_drive.jpg" alt="Conductrice" className="h-48 w-full rounded-md object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="bg-gray-50 py-4 mt-4 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="mb-1 text-center">
            <h2 className="text-primary dark:text-primary font-heading mb-4 text-3xl font-bold uppercase">TÉMOIGNAGES</h2>
            <div className="bg-primary mx-auto mb-4 h-1 w-24"></div>
            <p className="text-muted-foreground mx-auto max-w-2xl dark:text-gray-300">Découvrez ce que nos clients disent de nos services</p>
          </div>
          <TestimonialsFr />
        </div>
      </section>

      {/* Contact */}
      <section className="bg-primary dark:bg-primary/90 py-12 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2">
            <div>
              <h2 className="font-heading mb-6 text-3xl font-bold uppercase">CONTACTEZ-NOUS</h2>
              <p className="mb-6">
                Pour toute question concernant nos stages de récupération de points ou nos tests psychotechniques, n'hésitez pas à nous contacter.
                Notre équipe est à votre disposition pour vous aider.
              </p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <MapPin className="mt-1 mr-4 h-6 w-6 flex-shrink-0" />
                  <p>Palais Vauban 12 av Jean Moulin 83000 TOULON</p>
                </div>
                <div className="flex items-center">
                  <Mail className="mr-4 h-6 w-6 flex-shrink-0" />
                  <p><EMAIL></p>
                </div>
                <div className="flex items-center">
                  <Phone className="mr-4 h-6 w-6 flex-shrink-0" />
                  <p>06 58 77 23 85</p>
                </div>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg dark:bg-gray-800">
                <h3 className="text-primary dark:text-primary font-heading mb-6 text-center text-xl font-bold uppercase">Besoin d'informations ?</h3>
                <Button size="lg" className="bg-primary hover:bg-primary/90 w-full font-bold dark:text-white" asChild>
                  <Link href={route('contact')}>NOUS CONTACTER</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </FrontLayout>
  );
}


