<?php

namespace App\Http\Controllers;

use App\Models\Reservation;
use App\Services\SumUpService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class PaiementController extends Controller
{
    public function show($reservationId)
    {
        // Vérifier si l'utilisateur est connecté
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Récupérer la réservation
        $reservation = Reservation::with(['stage.lieu.ville.departement', 'user', 'typeStage'])
            ->where('user_id', Auth::id())
            ->findOrFail($reservationId);

        // Afficher la page de paiement
        return Inertia::render('Front/Paiement', [
            'reservation' => $reservation,
        ]);
    }

    public function process(Request $request, $reservationId)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'payment_method' => 'required|string|in:paypal,sumup,virement,cheque,bon',
        ]);

        // Récupérer la réservation
        $reservation = Reservation::where('user_id', Auth::id())->findOrFail($reservationId);

        if ($validated['payment_method'] === 'paypal') {
            // Créer une instance PayPal
            $provider = new PayPalClient;
            $provider->setApiCredentials(config('paypal'));
            $provider->getAccessToken();

            // Créer un ordre PayPal
            $response = $provider->createOrder([
                'intent' => 'CAPTURE',
                'application_context' => [
                    'return_url' => route('paypal.success', $reservationId),
                    'cancel_url' => route('paypal.cancel', $reservationId),
                ],
                'purchase_units' => [
                    [
                        'reference_id' => $reservation->id,
                        'description' => 'Réservation stage permis #' . $reservation->id,
                        'custom_id' => $reservation->id,
                        'amount' => [
                            'currency_code' => 'EUR',
                            'value' => $reservation->stage->prix,
                        ],
                    ],
                ],
            ]);

            if (isset($response['id']) && $response['id'] != null) {
                // Mettre à jour la réservation avec l'ID de la transaction PayPal
                $reservation->update([
                    'transaction_id' => $response['id'],
                    'methode_paiement' => 'paypal',
                ]);

                // Rediriger vers PayPal pour le paiement
                foreach ($response['links'] as $link) {
                    if ($link['rel'] === 'approve') {
                        return redirect()->away($link['href']);
                    }
                }
            }

            // En cas d'erreur
            return redirect()->back()->with('error', 'Une erreur est survenue lors de la création du paiement PayPal.');
        } elseif ($validated['payment_method'] === 'sumup') {
            // Créer une instance SumUp
            $sumupService = new SumUpService();

            // Créer un checkout SumUp
            $checkoutRef = 'reservation-' . $reservation->id . '-' . time();
            $description = 'Réservation stage permis #' . $reservation->id;
            $returnUrl = route('sumup.success', $reservationId);

            $response = $sumupService->createCheckout(
                $reservation->stage->prix,
                config('sumup.currency', 'EUR'),
                $checkoutRef,
                $description,
                $returnUrl
            );

            if ($response['success']) {
                // Mettre à jour la réservation avec l'ID de la transaction SumUp
                $reservation->update([
                    'transaction_id' => $response['checkout_id'],
                    'methode_paiement' => 'sumup',
                ]);

                // Rediriger vers SumUp pour le paiement
                return redirect()->away($response['checkout_url']);
            }

            // En cas d'erreur
            return redirect()->back()->with('error', 'Une erreur est survenue lors de la création du paiement SumUp: ' . ($response['error'] ?? 'Erreur inconnue'));
        } else {
            // Autres méthodes de paiement (virement, chèque, bon)
            $statut = 'en attente';
            $methode = $validated['payment_method'];

            $reservation->update([
                'statut' => $statut,
                'methode_paiement' => $methode,
            ]);

            // Rediriger vers la page de confirmation
            return redirect()->route('reservation.confirmation', $reservationId);
        }
    }

    public function paypalSuccess($reservationId)
    {
        // Récupérer la réservation
        $reservation = Reservation::where('user_id', Auth::id())->findOrFail($reservationId);

        // Vérifier que la réservation a un ID de transaction PayPal
        if (!$reservation->transaction_id) {
            return redirect()->route('paiement', $reservationId)->with('error', 'Aucune transaction PayPal trouvée.');
        }

        // Créer une instance PayPal
        $provider = new PayPalClient;
        $provider->setApiCredentials(config('paypal'));
        $provider->getAccessToken();

        // Capturer le paiement
        $response = $provider->capturePaymentOrder($reservation->transaction_id);

        if (isset($response['status']) && $response['status'] === 'COMPLETED') {
            // Mettre à jour la réservation
            $reservation->update([
                'statut' => 'confirmée',
                'date_paiement' => now(),
            ]);

            // Rediriger vers la page de confirmation
            return redirect()->route('reservation.confirmation', $reservationId);
        }

        // En cas d'erreur
        return redirect()->route('paiement', $reservationId)->with('error', 'Le paiement PayPal n\'a pas pu être complété.');
    }

    public function paypalCancel($reservationId)
    {
        // Rediriger vers la page de paiement avec un message d'erreur
        return redirect()->route('paiement', $reservationId)->with('error', 'Le paiement PayPal a été annulé.');
    }

    public function paypalWebhook(Request $request)
    {
        // Handle OPTIONS preflight request
        if ($request->isMethod('OPTIONS')) {
            return response('', 200)
                ->header('Access-Control-Allow-Origin', '*')
                ->header('Access-Control-Allow-Methods', 'POST, OPTIONS')
                ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN')
                ->header('Access-Control-Allow-Credentials', 'true')
                ->header('Access-Control-Max-Age', '86400');
        }

        // Vérifier l'authenticité de la requête PayPal (à implémenter)

        // Traiter la notification
        $payload = $request->all();
        Log::info('PayPal Webhook', $payload);

        // Vérifier le type d'événement
        if (isset($payload['event_type']) && $payload['event_type'] === 'PAYMENT.CAPTURE.COMPLETED') {
            // Récupérer l'ID de la réservation depuis custom_id
            $reservationId = $payload['resource']['custom_id'] ?? null;

            if ($reservationId) {
                $reservation = Reservation::find($reservationId);

                if ($reservation) {
                    // Mettre à jour la réservation
                    $reservation->update([
                        'statut' => 'confirmée',
                        'date_paiement' => now(),
                    ]);
                }
            }
        }

        // Ajouter des en-têtes CORS spécifiques pour PayPal
        return response()->json(['status' => 'success'])
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'POST, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN')
            ->header('Access-Control-Allow-Credentials', 'true');
    }

    public function sumupSuccess($reservationId)
    {
        // Récupérer la réservation
        $reservation = Reservation::where('user_id', Auth::id())->findOrFail($reservationId);

        // Vérifier que la réservation a un ID de transaction SumUp
        if (!$reservation->transaction_id) {
            return redirect()->route('paiement', $reservationId)->with('error', 'Aucune transaction SumUp trouvée.');
        }

        // Créer une instance SumUp
        $sumupService = new SumUpService();

        // Vérifier le statut du paiement
        $response = $sumupService->findCheckoutById($reservation->transaction_id);

        if ($response['success'] && isset($response['checkout']->status) && $response['checkout']->status === 'PAID') {
            // Mettre à jour la réservation
            $reservation->update([
                'statut' => 'confirmée',
                'date_paiement' => now(),
            ]);

            // Rediriger vers la page de confirmation
            return redirect()->route('reservation.confirmation', $reservationId);
        }

        // En cas d'erreur
        return redirect()->route('paiement', $reservationId)->with('error', 'Le paiement SumUp n\'a pas pu être complété.');
    }

    public function confirmation($reservationId)
    {
        // Récupérer la réservation
        $reservation = Reservation::with(['stage.lieu.ville.departement', 'user', 'typeStage'])
            ->where('user_id', Auth::id())
            ->findOrFail($reservationId);

        // Afficher la page de confirmation
        return Inertia::render('Front/Confirmation', [
            'reservation' => $reservation,
        ]);
    }

    // Méthodes pour les tests psychotechniques

    public function showTest($reservationId)
    {
        // Vérifier si l'utilisateur est connecté
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Récupérer la réservation
        $reservation = \App\Models\ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'user', 'typeTestPsycho'])
            ->where('user_id', Auth::id())
            ->findOrFail($reservationId);

        // Afficher la page de paiement
        return Inertia::render('Front/PaiementTest', [
            'reservation' => $reservation,
        ]);
    }

    public function processTest(Request $request, $reservationId)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'payment_method' => 'required|string|in:paypal,sumup,virement,cheque,bon',
        ]);

        // Récupérer la réservation
        $reservation = \App\Models\ReservationTestPsycho::where('user_id', Auth::id())->findOrFail($reservationId);

        if ($validated['payment_method'] === 'paypal') {
            // Créer une instance PayPal
            $provider = new PayPalClient;
            $provider->setApiCredentials(config('paypal'));
            $provider->getAccessToken();

            // Créer un ordre PayPal
            $response = $provider->createOrder([
                'intent' => 'CAPTURE',
                'application_context' => [
                    'return_url' => route('paypal.success.test', $reservationId),
                    'cancel_url' => route('paypal.cancel.test', $reservationId),
                ],
                'purchase_units' => [
                    [
                        'reference_id' => $reservation->id,
                        'description' => 'Réservation test psychotechnique #' . $reservation->id,
                        'custom_id' => $reservation->id,
                        'amount' => [
                            'currency_code' => 'EUR',
                            'value' => $reservation->testPsycho->prix,
                        ],
                    ],
                ],
            ]);

            if (isset($response['id']) && $response['id'] != null) {
                // Mettre à jour la réservation avec l'ID de la transaction PayPal
                $reservation->update([
                    'transaction_id' => $response['id'],
                    'methode_paiement' => 'paypal',
                ]);

                // Rediriger vers PayPal pour le paiement
                foreach ($response['links'] as $link) {
                    if ($link['rel'] === 'approve') {
                        return redirect()->away($link['href']);
                    }
                }
            }

            // En cas d'erreur
            return redirect()->back()->with('error', 'Une erreur est survenue lors de la création du paiement PayPal.');
        } elseif ($validated['payment_method'] === 'sumup') {
            // Créer une instance SumUp
            $sumupService = new SumUpService();

            // Créer un checkout SumUp
            $checkoutRef = 'reservation-test-' . $reservation->id . '-' . time();
            $description = 'Réservation test psychotechnique #' . $reservation->id;
            $returnUrl = route('sumup.success.test', $reservationId);

            $response = $sumupService->createCheckout(
                $reservation->testPsycho->prix,
                config('sumup.currency', 'EUR'),
                $checkoutRef,
                $description,
                $returnUrl
            );

            if ($response['success']) {
                // Mettre à jour la réservation avec l'ID de la transaction SumUp
                $reservation->update([
                    'transaction_id' => $response['checkout_id'],
                    'methode_paiement' => 'sumup',
                ]);

                // Rediriger vers SumUp pour le paiement
                return redirect()->away($response['checkout_url']);
            }

            // En cas d'erreur
            return redirect()->back()->with('error', 'Une erreur est survenue lors de la création du paiement SumUp: ' . ($response['error'] ?? 'Erreur inconnue'));
        } else {
            // Autres méthodes de paiement (virement, chèque, bon)
            $statut = 'en attente';
            $methode = $validated['payment_method'];

            $reservation->update([
                'statut' => $statut,
                'methode_paiement' => $methode,
            ]);

            // Rediriger vers la page de confirmation
            return redirect()->route('reservation-test.confirmation', $reservationId);
        }
    }

    public function paypalSuccessTest($reservationId)
    {
        // Récupérer la réservation
        $reservation = \App\Models\ReservationTestPsycho::where('user_id', Auth::id())->findOrFail($reservationId);

        // Vérifier que la réservation a un ID de transaction PayPal
        if (!$reservation->transaction_id) {
            return redirect()->route('paiement-test', $reservationId)->with('error', 'Aucune transaction PayPal trouvée.');
        }

        // Créer une instance PayPal
        $provider = new PayPalClient;
        $provider->setApiCredentials(config('paypal'));
        $provider->getAccessToken();

        // Capturer le paiement
        $response = $provider->capturePaymentOrder($reservation->transaction_id);

        if (isset($response['status']) && $response['status'] === 'COMPLETED') {
            // Mettre à jour la réservation
            $reservation->update([
                'statut' => 'confirmée',
                'date_paiement' => now(),
            ]);

            // Rediriger vers la page de confirmation
            return redirect()->route('reservation-test.confirmation', $reservationId);
        }

        // En cas d'erreur
        return redirect()->route('paiement-test', $reservationId)->with('error', 'Le paiement PayPal n\'a pas pu être complété.');
    }

    public function paypalCancelTest($reservationId)
    {
        // Rediriger vers la page de paiement avec un message d'erreur
        return redirect()->route('paiement-test', $reservationId)->with('error', 'Le paiement PayPal a été annulé.');
    }

    public function sumupSuccessTest($reservationId)
    {
        // Récupérer la réservation
        $reservation = \App\Models\ReservationTestPsycho::where('user_id', Auth::id())->findOrFail($reservationId);

        // Vérifier que la réservation a un ID de transaction SumUp
        if (!$reservation->transaction_id) {
            return redirect()->route('paiement-test', $reservationId)->with('error', 'Aucune transaction SumUp trouvée.');
        }

        // Créer une instance SumUp
        $sumupService = new SumUpService();

        // Vérifier le statut du paiement
        $response = $sumupService->findCheckoutById($reservation->transaction_id);

        if ($response['success'] && isset($response['checkout']->status) && $response['checkout']->status === 'PAID') {
            // Mettre à jour la réservation
            $reservation->update([
                'statut' => 'confirmée',
                'date_paiement' => now(),
            ]);

            // Rediriger vers la page de confirmation
            return redirect()->route('reservation-test.confirmation', $reservationId);
        }

        // En cas d'erreur
        return redirect()->route('paiement-test', $reservationId)->with('error', 'Le paiement SumUp n\'a pas pu être complété.');
    }

    public function confirmationTest($reservationId)
    {
        // Récupérer la réservation
        $reservation = \App\Models\ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'user', 'typeTestPsycho'])
            ->where('user_id', Auth::id())
            ->findOrFail($reservationId);

        // Afficher la page de confirmation
        return Inertia::render('Front/ConfirmationTest', [
            'reservation' => $reservation,
        ]);
    }
}
