<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Stage extends Model
{
    use HasFactory;

    // Allow explicit setting of IDs
    public $incrementing = true;

    // Define the ID type
    protected $keyType = 'integer';

    protected $fillable = [
        'id',
        'date_debut',
        'date_fin',
        'lieu_id',
        'places_disponibles',
        'prix',
        'reference'
    ];

    protected $casts = [
        'date_debut' => 'date',
        'date_fin' => 'date',
        'prix' => 'decimal:2'
    ];

    public function lieu()
    {
        return $this->belongsTo(Lieu::class);
    }

    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }
}
