# Stack technique et fonctionnalités pour la plateforme de stage

## Stack technique

### Backend: Lara<PERSON>

1. Créer un nouveau projet Laravel.
2. Configurer la base de données dans le fichier `.env`.
3. <PERSON><PERSON><PERSON> les migrations pour les tables suivantes :
   - departements
   - villes
   - lieux
   - stages
   - reservations
   - users
   - type_stages
4. Générer les modèles Eloquent correspondants avec leurs relations.
5. Implémenter les contrôleurs pour gérer les départements, villes, lieux, stages, réservations et types de stages.
6. Configurer les routes API.

### Frontend: Inertia + ReactJS + shadcn UI

1. Installer Inertia et ses dépendances. (déjà fais)
2. Configurer Inertia dans le projet Laravel.(déjà fais)
3. C<PERSON>er les composants React pour : 
   - Liste des départements et villes disponibles.
   - Liste des lieux associés à une ville.
   - Liste des stages disponibles par lieu.
   - **Formulaire de réservation (Mis à jour pour inclure les nouveaux champs)**.
   - Tableau de bord utilisateur.
   - Interface d'administration pour gérer les départements, villes, lieux, stages et types de stages.
4. Utiliser Inertia pour lier les composants React aux routes Laravel.

---

## Fonctionnalités à implémenter (Mise à jour)

1. Gestion des départements, villes et lieux dans l'interface d'administration.
2. Gestion des types de stages dans l'interface d'administration.
3. Affichage des stages disponibles par lieu ou par ville sur le frontend.
4. Système de réservation en ligne avec gestion des places disponibles et les nouveaux champs (date infraction, etc.).
5. Upload des fichiers (permis recto/verso, lettre 48N).
6. Authentification et autorisation des utilisateurs.
7. Tableau de bord utilisateur pour consulter ses réservations et télécharger ses documents.
8. Interface d'administration pour gérer toutes les entités.

---

## Étapes de développement (Mise à jour)

1. Configurer l'environnement de développement.
2. Créer la structure de la base de données avec migrations Laravel.
3. Développer les API backend avec Laravel pour chaque entité (y compris les endpoints pour l'upload des fichiers).
4. Créer les composants frontend avec ReactJS pour chaque fonctionnalité, en intégrant les nouveaux champs.
5. Intégrer Inertia pour connecter le backend aux composants ReactJS.
6. Implémenter l'authentification et l'autorisation.
7. Tester toutes les fonctionnalités en local, en particulier l'upload des fichiers.
8. Optimiser les performances du backend et du frontend.
9. Déployer l'application.

Ce plan intègre tous les champs et tables nécessaires pour la réservation, tout en conservant l'architecture Laravel, Inertia et ReactJS.  L'attention est portée sur la gestion des fichiers et l'ajout des informations spécifiques aux infractions.
